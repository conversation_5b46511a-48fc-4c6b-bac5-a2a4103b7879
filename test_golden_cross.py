#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多指标金叉策略
"""

import sys
sys.path.append('.')
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def test_golden_cross_strategy():
    """测试多指标金叉策略"""
    print("🎯 测试多指标金叉策略")
    print("=" * 60)
    
    try:
        system = OKXOptimizedTradingSystem()
        
        # 测试CFX和其他币种
        test_symbols = ['CFX-USDT-SWAP', 'BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'SOL-USDT-SWAP']
        
        for symbol in test_symbols:
            print(f"\n📊 分析 {symbol}:")
            print("-" * 40)
            
            analysis = system.analyze_signal(symbol)
            if analysis:
                print(f"   信号: {analysis['signal']:>4}")
                print(f"   置信度: {analysis['confidence']:>3}%")
                print(f"   价格: {analysis['price']:>10.4f}")
                print(f"   MA50偏离: {analysis['ma50_diff']:>6.2f}%")
                print(f"   RSI: {analysis['rsi']:>6.1f}")
                kdj = analysis['kdj']
                print(f"   KDJ: K={kdj['k']:.1f}, D={kdj['d']:.1f}, J={kdj['j']:.1f}")
                print(f"   金叉指标数: {analysis['golden_cross_count']}/5")
                
                if analysis['signals']:
                    print("   信号详情:")
                    for sig in analysis['signals']:
                        print(f"     • {sig}")
                
                if analysis['patterns']:
                    print(f"   K线形态: {analysis['patterns']}")
                
                # 检查是否为高质量信号
                if analysis['confidence'] >= 75:
                    print(f"   🎯 高质量信号!")
                    risk_ok, risk_msg = system.check_optimized_risk_limits(symbol, analysis['confidence'])
                    print(f"   风险检查: {'✅ 通过' if risk_ok else '❌ 拒绝'} - {risk_msg}")
                    
                    if risk_ok and analysis['signal'] != 'HOLD':
                        print(f"   💰 可执行交易: {analysis['signal']}")
                
            else:
                print(f"   ❌ 无法分析")
        
        print("\n🔍 寻找最佳金叉信号...")
        symbols = system.get_all_symbols()[:20]  # 测试前20个币种
        
        best_signals = []
        for symbol in symbols:
            analysis = system.analyze_signal(symbol)
            if analysis and analysis['golden_cross_count'] >= 2:
                best_signals.append(analysis)
        
        # 按金叉数量和置信度排序
        best_signals.sort(key=lambda x: (x['golden_cross_count'], x['confidence']), reverse=True)
        
        print(f"\n🏆 发现 {len(best_signals)} 个多指标共振信号:")
        for i, signal in enumerate(best_signals[:5]):  # 显示前5个
            print(f"{i+1}. {signal['symbol']:20} | {signal['signal']:>4} | {signal['confidence']:>3}% | 金叉:{signal['golden_cross_count']}/4")
            print(f"   信号: {', '.join(signal['signals'][:3])}")  # 显示前3个信号
        
        if not best_signals:
            print("   当前市场暂无明显的多指标金叉信号")
            print("   建议等待更好的入场时机")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_golden_cross_strategy()
